import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import seaborn as sns

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_excel_data(file_path):
    """Load the Excel file and examine its structure"""
    try:
        # Read all sheet names
        excel_file = pd.ExcelFile(file_path)
        print("Available sheets:", excel_file.sheet_names)

        # Look for the daily PnL sheet (case insensitive)
        pnl_sheet = None
        for sheet in excel_file.sheet_names:
            if 'dail' in sheet.lower() and 'pnl' in sheet.lower():
                pnl_sheet = sheet
                break

        if pnl_sheet is None:
            print("Could not find 'dail pnl' sheet. Available sheets:")
            for sheet in excel_file.sheet_names:
                print(f"  - {sheet}")
            return None

        print(f"Found PnL sheet: '{pnl_sheet}'")

        # Read the PnL data
        df = pd.read_excel(file_path, sheet_name=pnl_sheet)
        print(f"\nData shape: {df.shape}")
        print(f"\nColumn names: {list(df.columns)}")
        print(f"\nFirst few rows:")
        print(df.head())

        return df, pnl_sheet

    except Exception as e:
        print(f"Error loading Excel file: {e}")
        return None

def prepare_pnl_data(df):
    """Prepare the PnL data for analysis"""
    # Try to identify date and PnL columns
    date_col = None
    pnl_col = None

    # Look for date column
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['date', 'time', 'day']):
            date_col = col
            break

    # Look for PnL column
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['pnl', 'p&l', 'profit', 'loss', 'return']):
            pnl_col = col
            break

    if date_col is None or pnl_col is None:
        print("Could not automatically identify date and PnL columns.")
        print("Available columns:", list(df.columns))
        return None

    print(f"Using date column: '{date_col}'")
    print(f"Using PnL column: '{pnl_col}'")

    # Clean and prepare data
    df_clean = df[[date_col, pnl_col]].copy()
    df_clean = df_clean.dropna()

    # Convert date column to datetime
    try:
        df_clean[date_col] = pd.to_datetime(df_clean[date_col])
    except:
        print("Warning: Could not convert date column to datetime")

    # Ensure PnL column is numeric
    df_clean[pnl_col] = pd.to_numeric(df_clean[pnl_col], errors='coerce')
    df_clean = df_clean.dropna()

    # Sort by date
    df_clean = df_clean.sort_values(date_col)
    df_clean = df_clean.reset_index(drop=True)

    return df_clean, date_col, pnl_col

def calculate_drawdown(pnl_series):
    """Calculate drawdown from PnL series"""
    # Calculate cumulative PnL
    cumulative_pnl = pnl_series.cumsum()

    # Calculate running maximum (peak)
    running_max = cumulative_pnl.expanding().max()

    # Calculate drawdown
    drawdown = cumulative_pnl - running_max

    # Calculate drawdown percentage
    drawdown_pct = (drawdown / running_max.replace(0, np.nan)) * 100

    return cumulative_pnl, running_max, drawdown, drawdown_pct

def plot_drawdown_chart(df, date_col, pnl_col):
    """Create comprehensive drawdown analysis charts"""
    # Calculate drawdown metrics
    cumulative_pnl, running_max, drawdown, drawdown_pct = calculate_drawdown(df[pnl_col])

    # Create figure with subplots
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    fig.suptitle('PnL and Drawdown Analysis', fontsize=16, fontweight='bold')

    # Plot 1: Cumulative PnL and Running Maximum
    axes[0].plot(df[date_col], cumulative_pnl, label='Cumulative PnL', linewidth=2, color='blue')
    axes[0].plot(df[date_col], running_max, label='Running Maximum', linewidth=2, color='green', alpha=0.7)
    axes[0].fill_between(df[date_col], cumulative_pnl, running_max, alpha=0.3, color='red', label='Drawdown')
    axes[0].set_title('Cumulative PnL and Drawdown')
    axes[0].set_ylabel('Cumulative PnL')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # Plot 2: Drawdown in absolute terms
    axes[1].fill_between(df[date_col], drawdown, 0, alpha=0.7, color='red')
    axes[1].plot(df[date_col], drawdown, color='darkred', linewidth=1)
    axes[1].set_title('Drawdown (Absolute)')
    axes[1].set_ylabel('Drawdown Amount')
    axes[1].grid(True, alpha=0.3)

    # Plot 3: Daily PnL with loss days highlighted
    colors = ['red' if x < 0 else 'green' for x in df[pnl_col]]
    axes[2].bar(df[date_col], df[pnl_col], color=colors, alpha=0.7, width=1)
    axes[2].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    axes[2].set_title('Daily PnL (Loss Days in Red)')
    axes[2].set_ylabel('Daily PnL')
    axes[2].set_xlabel('Date')
    axes[2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.xticks(rotation=45)

    # Save the chart instead of showing it
    plt.savefig('drawdown_analysis.png', dpi=300, bbox_inches='tight')
    print("Drawdown chart saved as 'drawdown_analysis.png'")
    plt.close()

    return cumulative_pnl, drawdown

def analyze_loss_days(df, date_col, pnl_col):
    """Analyze loss-making days"""
    loss_days = df[df[pnl_col] < 0].copy()

    print(f"\n=== LOSS DAYS ANALYSIS ===")
    print(f"Total trading days: {len(df)}")
    print(f"Loss-making days: {len(loss_days)}")
    print(f"Loss day percentage: {len(loss_days)/len(df)*100:.1f}%")
    print(f"Total losses: {loss_days[pnl_col].sum():.2f}")
    print(f"Average loss per loss day: {loss_days[pnl_col].mean():.2f}")
    print(f"Largest single day loss: {loss_days[pnl_col].min():.2f}")

    print(f"\n=== TOP 10 WORST LOSS DAYS ===")
    worst_days = loss_days.nsmallest(10, pnl_col)
    for idx, row in worst_days.iterrows():
        print(f"{row[date_col].strftime('%Y-%m-%d') if hasattr(row[date_col], 'strftime') else row[date_col]}: {row[pnl_col]:.2f}")

    return loss_days

def main():
    file_path = "xls_positions-3904345250525.xlsx"

    # Load data
    result = load_excel_data(file_path)
    if result is None:
        return

    df, sheet_name = result

    # Prepare data
    prepared_data = prepare_pnl_data(df)
    if prepared_data is None:
        return

    df_clean, date_col, pnl_col = prepared_data

    print(f"\nProcessed {len(df_clean)} rows of data")
    print(f"Date range: {df_clean[date_col].min()} to {df_clean[date_col].max()}")
    print(f"Total PnL: {df_clean[pnl_col].sum():.2f}")

    # Create drawdown chart
    cumulative_pnl, drawdown = plot_drawdown_chart(df_clean, date_col, pnl_col)

    # Analyze loss days
    loss_days = analyze_loss_days(df_clean, date_col, pnl_col)

    # Calculate drawdown statistics
    max_drawdown = drawdown.min()
    max_drawdown_date = df_clean.loc[drawdown.idxmin(), date_col]

    print(f"\n=== DRAWDOWN STATISTICS ===")
    print(f"Maximum drawdown: {max_drawdown:.2f}")
    print(f"Maximum drawdown date: {max_drawdown_date}")
    print(f"Current drawdown: {drawdown.iloc[-1]:.2f}")

if __name__ == "__main__":
    main()
