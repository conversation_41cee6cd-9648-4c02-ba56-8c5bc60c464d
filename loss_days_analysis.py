import pandas as pd
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def create_loss_days_chart():
    """Create a detailed chart focusing on loss-making days"""
    
    # Load the data
    file_path = "xls_positions-3904345250525.xlsx"
    df = pd.read_excel(file_path, sheet_name='Daily pnl')
    
    # Clean data
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    # Identify loss days
    loss_days = df[df['pnl'] < 0].copy()
    
    # Create figure with multiple subplots
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('Loss Days Analysis - Detailed View', fontsize=16, fontweight='bold')
    
    # Plot 1: Loss days over time (scatter plot)
    axes[0, 0].scatter(loss_days['date'], loss_days['pnl'], 
                      color='red', alpha=0.7, s=50)
    axes[0, 0].set_title('Loss Days Over Time')
    axes[0, 0].set_ylabel('Loss Amount')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Plot 2: Loss distribution histogram
    axes[0, 1].hist(loss_days['pnl'], bins=20, color='red', alpha=0.7, edgecolor='black')
    axes[0, 1].set_title('Distribution of Loss Amounts')
    axes[0, 1].set_xlabel('Loss Amount')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Monthly loss analysis
    loss_days['month'] = loss_days['date'].dt.to_period('M')
    monthly_losses = loss_days.groupby('month').agg({
        'pnl': ['sum', 'count', 'mean']
    }).round(2)
    monthly_losses.columns = ['Total_Loss', 'Loss_Days', 'Avg_Loss']
    
    x_pos = range(len(monthly_losses))
    axes[1, 0].bar(x_pos, monthly_losses['Total_Loss'], color='red', alpha=0.7)
    axes[1, 0].set_title('Monthly Total Losses')
    axes[1, 0].set_ylabel('Total Loss Amount')
    axes[1, 0].set_xlabel('Month')
    axes[1, 0].set_xticks(x_pos)
    axes[1, 0].set_xticklabels([str(m) for m in monthly_losses.index], rotation=45)
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: Loss days frequency by month
    axes[1, 1].bar(x_pos, monthly_losses['Loss_Days'], color='orange', alpha=0.7)
    axes[1, 1].set_title('Number of Loss Days per Month')
    axes[1, 1].set_ylabel('Number of Loss Days')
    axes[1, 1].set_xlabel('Month')
    axes[1, 1].set_xticks(x_pos)
    axes[1, 1].set_xticklabels([str(m) for m in monthly_losses.index], rotation=45)
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('loss_days_detailed_analysis.png', dpi=300, bbox_inches='tight')
    print("Loss days detailed chart saved as 'loss_days_detailed_analysis.png'")
    plt.close()
    
    # Create a summary table
    print("\n=== MONTHLY LOSS SUMMARY ===")
    print(monthly_losses.to_string())
    
    return loss_days, monthly_losses

def create_loss_calendar_heatmap():
    """Create a calendar heatmap showing loss days"""
    
    # Load the data
    file_path = "xls_positions-3904345250525.xlsx"
    df = pd.read_excel(file_path, sheet_name='Daily pnl')
    
    # Clean data
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    # Create a pivot table for heatmap
    df['year'] = df['date'].dt.year
    df['month'] = df['date'].dt.month
    df['day'] = df['date'].dt.day
    
    # Create separate heatmaps for each year
    years = df['year'].unique()
    
    fig, axes = plt.subplots(len(years), 1, figsize=(15, 6*len(years)))
    if len(years) == 1:
        axes = [axes]
    
    fig.suptitle('Daily PnL Calendar Heatmap', fontsize=16, fontweight='bold')
    
    for i, year in enumerate(years):
        year_data = df[df['year'] == year].copy()
        
        # Create a pivot table for the heatmap
        pivot_data = year_data.pivot_table(
            values='pnl', 
            index='month', 
            columns='day', 
            fill_value=0
        )
        
        # Create heatmap
        sns.heatmap(pivot_data, 
                   cmap='RdYlGn', 
                   center=0,
                   annot=False,
                   fmt='.0f',
                   cbar_kws={'label': 'PnL'},
                   ax=axes[i])
        
        axes[i].set_title(f'Daily PnL Heatmap - {year}')
        axes[i].set_xlabel('Day of Month')
        axes[i].set_ylabel('Month')
    
    plt.tight_layout()
    plt.savefig('pnl_calendar_heatmap.png', dpi=300, bbox_inches='tight')
    print("PnL calendar heatmap saved as 'pnl_calendar_heatmap.png'")
    plt.close()

def main():
    print("Creating detailed loss days analysis...")
    loss_days, monthly_losses = create_loss_days_chart()
    
    print("\nCreating calendar heatmap...")
    create_loss_calendar_heatmap()
    
    print("\n=== SUMMARY STATISTICS ===")
    print(f"Worst single day loss: {loss_days['pnl'].min():.2f}")
    print(f"Average loss per loss day: {loss_days['pnl'].mean():.2f}")
    print(f"Median loss per loss day: {loss_days['pnl'].median():.2f}")
    print(f"Standard deviation of losses: {loss_days['pnl'].std():.2f}")
    
    # Calculate consecutive loss days
    file_path = "xls_positions-3904345250525.xlsx"
    df = pd.read_excel(file_path, sheet_name='Daily pnl')
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    
    df['is_loss'] = df['pnl'] < 0
    df['loss_group'] = (df['is_loss'] != df['is_loss'].shift()).cumsum()
    
    consecutive_losses = df[df['is_loss']].groupby('loss_group').size()
    if len(consecutive_losses) > 0:
        max_consecutive = consecutive_losses.max()
        print(f"Maximum consecutive loss days: {max_consecutive}")
    else:
        print("No consecutive loss days found")

if __name__ == "__main__":
    main()
